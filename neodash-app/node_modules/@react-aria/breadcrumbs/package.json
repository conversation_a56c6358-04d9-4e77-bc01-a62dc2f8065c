{"name": "@react-aria/breadcrumbs", "version": "3.5.2", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"types": "./dist/types.d.ts", "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-aria/i18n": "^3.7.2", "@react-aria/interactions": "^3.15.1", "@react-aria/link": "^3.5.1", "@react-aria/utils": "^3.17.0", "@react-types/breadcrumbs": "^3.5.2", "@react-types/shared": "^3.18.1", "@swc/helpers": "^0.4.14"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5911ed21de4b76d66f6254c02302519e02d50e16"}