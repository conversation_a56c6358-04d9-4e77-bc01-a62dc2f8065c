{"name": "@react-aria/combobox", "version": "3.6.1", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"types": "./dist/types.d.ts", "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-aria/i18n": "^3.7.2", "@react-aria/interactions": "^3.15.1", "@react-aria/listbox": "^3.9.1", "@react-aria/live-announcer": "^3.3.0", "@react-aria/menu": "^3.9.1", "@react-aria/overlays": "^3.14.1", "@react-aria/selection": "^3.15.0", "@react-aria/textfield": "^3.9.2", "@react-aria/utils": "^3.17.0", "@react-stately/collections": "^3.8.0", "@react-stately/combobox": "^3.5.1", "@react-stately/layout": "^3.12.1", "@react-types/button": "^3.7.3", "@react-types/combobox": "^3.6.2", "@react-types/shared": "^3.18.1", "@swc/helpers": "^0.4.14"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5911ed21de4b76d66f6254c02302519e02d50e16"}