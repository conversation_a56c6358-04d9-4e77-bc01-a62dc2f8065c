# Changes to PostCSS Stepped Value Functions

### 2.1.0 (February 21, 2023)

- Removed: warnings on `var()` or otherwise unconvertible values (doesn't affect the output, non-breaking)
- Added: `@csstools/css-calc`
- Added: unit conversions (`mod(735ms, 0.1s)`)

### 2.0.1 (January 28, 2023)

- Improve `types` declaration in `package.json`

### 2.0.0 (January 24, 2023)

- Updated: Support for Node v14+ (major).

### 1.0.1 (July 8, 2022)

- Fix case insensitive matching.

### 1.0.0 (May 2, 2022)

- Initial version
