{"name": "@csstools/postcss-progressive-custom-properties", "description": "Correctly declare progressive enhancements for CSS Custom Properties.", "version": "2.1.1", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "devDependencies": {"@csstools/postcss-tape": "*"}, "scripts": {"build": "node ./generate/matchers.mjs && eslint --fix ./src/matchers.ts && rollup -c ../../rollup/default.mjs", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "test": "node .tape.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-progressive-custom-properties"}, "keywords": ["css", "custom", "declarations", "postcss", "postcss-plugin", "progressive", "properties", "utility", "variables", "vars"], "csstools": {"exportName": "postcssProgressiveCustomProperties", "humanReadableName": "PostCSS Progressive Custom Properties"}, "volta": {"extends": "../../package.json"}}