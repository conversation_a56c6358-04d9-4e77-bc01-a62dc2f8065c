{"name": "@csstools/postcss-oklab-function", "description": "Use oklab() and oklch() color functions in CSS", "version": "2.2.0", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/css-color-parser": "^1.0.0", "@csstools/css-parser-algorithms": "^2.0.1", "@csstools/css-tokenizer": "^2.1.0", "@csstools/postcss-progressive-custom-properties": "^2.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "devDependencies": {"@csstools/postcss-tape": "*"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "test": "node .tape.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-oklab-function#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-oklab-function"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["color", "css", "design", "display-p3", "oklab", "oklch", "postcss", "postcss-plugin", "syntax"], "csstools": {"exportName": "postcssOKLabFunction", "humanReadableName": "PostCSS OKLab Function"}, "volta": {"extends": "../../package.json"}}