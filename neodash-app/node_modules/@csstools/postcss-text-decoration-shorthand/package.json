{"name": "@csstools/postcss-text-decoration-shorthand", "description": "Use text-decoration in it's shorthand form in CSS", "version": "2.2.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/color-helpers": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "devDependencies": {"@csstools/postcss-tape": "*", "autoprefixer": "^10.4.8"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "test": "node .tape.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-text-decoration-shorthand#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-text-decoration-shorthand"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "postcss-plugin", "shorthand", "text-decoration", "text-decoration-thickness"], "csstools": {"cssdbId": "text-decoration-shorthand", "exportName": "postcssTextDecorationShorthand", "humanReadableName": "PostCSS Text Decoration Shorthand", "specUrl": "https://drafts.csswg.org/css-text-decor-4/#text-decoration-property"}, "volta": {"extends": "../../package.json"}}