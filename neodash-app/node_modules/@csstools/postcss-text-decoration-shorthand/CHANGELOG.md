# Changes to PostCSS Text Decoration Shorthand

### 2.2.2 (March 25, 2023)

- Add `color-mix` as a known color function.

### 2.2.1 (February 13, 2023)

- Updated: `preserve` option defaults to `true`

### 2.2.0 (February 2, 2023)

- Add: `@csstools/color-helpers` dependency for the named colors list.

### 2.1.0 (January 28, 2023)

- Add: support for multiple line values (`text-decoration: overline underline;`)

### 2.0.1 (January 28, 2023)

- Improve `types` declaration in `package.json`

### 2.0.0 (January 24, 2023)

- Updated: Support for Node v14+ (major).
- Add: `-webkit-text-decoration` shorthand for single value `text-decoration: underline;`. [autoprefixer 1473](https://github.com/postcss/autoprefixer/issues/1473#issuecomment-1243370592)

### 1.0.0 (August 15, 2022)

- Initial version
