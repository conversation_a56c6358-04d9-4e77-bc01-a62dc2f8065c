{"name": "@csstools/postcss-unset-value", "description": "Use the unset keyword in CSS.", "version": "2.0.1", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss": "^8.4"}, "scripts": {"prebuild": "npm run clean", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-unset-value#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-unset-value"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["inherit", "initial", "postcss-plugin", "unset"], "csstools": {"exportName": "postcssUnsetValue", "humanReadableName": "PostCSS Unset Value"}, "volta": {"extends": "../../package.json"}}