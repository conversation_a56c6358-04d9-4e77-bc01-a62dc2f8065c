# Changes to PostCSS Unset Value

### 2.0.1 (January 28, 2023)

- Improve `types` declaration in `package.json`

### 2.0.0 (January 24, 2023)

- Updated: Support for Node v14+ (major).

### 1.0.2 (July 8, 2022)

- Fixed: Case insensitive property and keyword matching.

### 1.0.1 (May 11, 2022)

- No longer converts `all: unset;` to `all: initial;`. `all: unset` is now ignored.

### 1.0.0 (February 21, 2022)

- Initial version
