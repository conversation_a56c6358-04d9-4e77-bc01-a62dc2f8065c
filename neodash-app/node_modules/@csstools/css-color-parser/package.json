{"name": "@csstools/css-color-parser", "description": "Parse CSS color values", "version": "1.1.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/color-helpers": "^2.0.0", "@csstools/css-calc": "^1.0.1"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^2.1.0", "@csstools/css-tokenizer": "^2.1.0"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "stryker": "stryker run --logLevel error", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/css-color-parser#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "packages/css-color-parser"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["color", "css", "parser"], "volta": {"extends": "../../package.json"}}