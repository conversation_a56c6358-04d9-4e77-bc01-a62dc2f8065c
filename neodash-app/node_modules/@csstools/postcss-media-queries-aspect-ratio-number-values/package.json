{"name": "@csstools/postcss-media-queries-aspect-ratio-number-values", "description": "Use number values in aspect-ratio media queries.", "version": "1.0.1", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/css-parser-algorithms": "^2.0.0", "@csstools/css-tokenizer": "^2.0.0", "@csstools/media-query-list-parser": "^2.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {"prebuild": "npm run clean", "build": "rollup -c ../../rollup/default.mjs", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true }); fs.mkdirSync('./dist');\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-media-queries-aspect-ratio-number-values#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-media-queries-aspect-ratio-number-values"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["postcss-plugin"], "csstools": {"cssdbId": "media-queries-aspect-ratio-number-values", "exportName": "postcssMediaQueriesAspectRatioNumberValues", "humanReadableName": "PostCSS Media Queries Aspect-Ratio Number Values", "specUrl": "https://www.w3.org/TR/mediaqueries-4/#aspect-ratio"}, "volta": {"extends": "../../package.json"}}