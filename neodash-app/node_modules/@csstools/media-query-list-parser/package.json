{"name": "@csstools/media-query-list-parser", "description": "Parse CSS media query lists.", "version": "2.0.2", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^14 || ^16 || >=18"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"@csstools/css-parser-algorithms": "^2.0.0", "@csstools/css-tokenizer": "^2.0.0"}, "scripts": {"build": "rollup -c ../../rollup/default.mjs", "lint": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run build && npm run test", "stryker": "stryker run --logLevel error", "test": "node ./test/test.mjs && node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node ./test/test.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/media-query-list-parser#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "packages/media-query-list-parser"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "media query", "parser"], "volta": {"extends": "../../package.json"}}