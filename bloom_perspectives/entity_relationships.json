{"perspective": {"name": "Entity Relationships", "description": "Visualize how entities relate to each other in the Graphiti knowledge graph", "version": "1.0", "created_for": "Graphiti Knowledge Graph", "node_categories": [{"label": "Entity", "styling": {"color": "#1f77b4", "border_color": "#0d5aa7", "border_width": 2, "size": {"type": "property", "property": "degree", "min_size": 15, "max_size": 50, "default_size": 25}, "icon": "person", "shape": "circle"}, "captions": [{"property": "name", "position": "bottom", "font_size": 12, "font_weight": "bold"}], "search_properties": ["name", "summary"], "tooltip_properties": ["name", "summary", "created_at", "group_id"], "filters": [{"property": "group_id", "type": "string", "default": "chatgpt_conversations"}, {"property": "created_at", "type": "datetime", "range": true}]}, {"label": "Community", "styling": {"color": "#2ca02c", "border_color": "#1a661a", "border_width": 3, "size": {"type": "fixed", "value": 40}, "icon": "group", "shape": "hexagon"}, "captions": [{"property": "name", "position": "center", "font_size": 14, "font_weight": "bold", "color": "white"}], "search_properties": ["name", "summary"], "tooltip_properties": ["name", "summary", "created_at", "group_id"]}], "relationship_categories": [{"type": "RELATES_TO", "styling": {"color": "#666666", "thickness": {"type": "property", "property": "weight", "min_thickness": 1, "max_thickness": 8, "default_thickness": 3}, "arrow_size": "medium", "line_style": "solid"}, "captions": [{"property": "fact", "position": "middle", "font_size": 10, "background": true, "max_length": 50}], "tooltip_properties": ["fact", "name", "created_at", "valid_at"], "filters": [{"property": "created_at", "type": "datetime", "range": true}]}, {"type": "BELONGS_TO", "styling": {"color": "#2ca02c", "thickness": 2, "arrow_size": "small", "line_style": "dashed"}, "captions": [], "tooltip_properties": ["created_at"]}], "layout": {"algorithm": "force_directed", "settings": {"gravity": 0.1, "repulsion": 1000, "link_distance": 100, "iterations": 300, "enable_physics": true}}, "default_queries": [{"name": "All Entities", "description": "Show all entities in the selected group", "cypher": "MATCH (n:Entity) WHERE n.group_id = $group_id RETURN n LIMIT 50", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Entity Network", "description": "Show entities and their relationships", "cypher": "MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity) WHERE n.group_id = $group_id RETURN n, r, m LIMIT 30", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Highly Connected Entities", "description": "Find entities with many relationships", "cypher": "MATCH (n:Entity) WHERE n.group_id = $group_id WITH n, size((n)-[:RELATES_TO]-()) as degree WHERE degree >= 3 MATCH (n)-[r:RELATES_TO]-(m:Entity) RETURN n, r, m ORDER BY degree DESC", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Entity Communities", "description": "Show entities grouped by communities", "cypher": "MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = $group_id RETURN c, e LIMIT 100", "parameters": {"group_id": "chatgpt_conversations"}}], "search_configuration": {"enable_fulltext": true, "search_properties": {"Entity": ["name", "summary"], "Community": ["name", "summary"]}, "fuzzy_search": true, "case_sensitive": false}, "export_settings": {"include_styling": true, "include_layout": true, "format": "json"}}, "metadata": {"created_by": "Graphiti Bloom Integration", "created_date": "2024-01-01", "version": "1.0", "compatible_with": ["Neo4j Bloom 2.0+"], "description": "Optimized perspective for exploring entity relationships in Graphiti knowledge graphs"}}