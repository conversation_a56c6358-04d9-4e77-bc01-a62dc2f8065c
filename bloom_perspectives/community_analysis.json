{"perspective": {"name": "Community Analysis", "description": "Analyze entity communities and clustering patterns in Graphiti", "version": "1.0", "created_for": "Graphiti Knowledge Graph", "node_categories": [{"label": "Community", "styling": {"color": "#2ca02c", "border_color": "#1a661a", "border_width": 3, "size": {"type": "property", "property": "member_count", "min_size": 30, "max_size": 80, "default_size": 50}, "icon": "group", "shape": "hexagon"}, "captions": [{"property": "name", "position": "center", "font_size": 14, "font_weight": "bold", "color": "white"}, {"property": "member_count", "position": "bottom", "font_size": 10, "color": "#2ca02c", "prefix": "Members: "}], "search_properties": ["name", "summary"], "tooltip_properties": ["name", "summary", "member_count", "created_at", "group_id"], "filters": [{"property": "group_id", "type": "string", "default": "chatgpt_conversations"}, {"property": "member_count", "type": "number", "range": true, "min": 1, "max": 100}]}, {"label": "Entity", "styling": {"color": {"type": "property", "property": "community_id", "color_scheme": "category20"}, "border_color": "#333333", "border_width": 1, "size": {"type": "property", "property": "degree", "min_size": 15, "max_size": 40, "default_size": 25}, "icon": "person", "shape": "circle"}, "captions": [{"property": "name", "position": "bottom", "font_size": 11, "font_weight": "normal"}], "search_properties": ["name", "summary"], "tooltip_properties": ["name", "summary", "community_id", "degree", "created_at"], "filters": [{"property": "community_id", "type": "string"}, {"property": "degree", "type": "number", "range": true}]}], "relationship_categories": [{"type": "BELONGS_TO", "styling": {"color": "#2ca02c", "thickness": 3, "arrow_size": "medium", "line_style": "solid"}, "captions": [], "tooltip_properties": ["created_at", "uuid"]}, {"type": "RELATES_TO", "styling": {"color": {"type": "conditional", "conditions": [{"condition": "same_community", "color": "#666666"}, {"condition": "cross_community", "color": "#ff4444"}], "default": "#999999"}, "thickness": {"type": "property", "property": "weight", "min_thickness": 1, "max_thickness": 6, "default_thickness": 2}, "arrow_size": "small", "line_style": "solid"}, "captions": [{"property": "fact", "position": "middle", "font_size": 9, "max_length": 30, "background": true}], "tooltip_properties": ["fact", "name", "created_at"], "filters": [{"property": "relationship_type", "type": "string", "options": ["intra_community", "inter_community"]}]}], "layout": {"algorithm": "community_detection", "settings": {"clustering_property": "community_id", "cluster_spacing": 200, "intra_cluster_spacing": 50, "enable_community_hulls": true, "hull_opacity": 0.2, "force_directed_within_clusters": true}}, "default_queries": [{"name": "All Communities", "description": "Show all communities in the graph", "cypher": "MATCH (c:Community) WHERE c.group_id = $group_id RETURN c ORDER BY c.member_count DESC LIMIT 20", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Community Structure", "description": "Show communities and their member entities", "cypher": "MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = $group_id RETURN c, e LIMIT 100", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Cross-Community Relationships", "description": "Find relationships between different communities", "cypher": "MATCH (e1:Entity)-[r:RELATES_TO]->(e2:Entity) WHERE e1.group_id = $group_id AND e1.community_id <> e2.community_id RETURN e1, r, e2 LIMIT 50", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Largest Communities", "description": "Show the largest communities by member count", "cypher": "MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity) WHERE c.group_id = $group_id WITH c, count(e) as size WHERE size >= 3 MATCH (c)<-[:BELONGS_TO]-(members:Entity) RETURN c, members ORDER BY size DESC", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Community Bridges", "description": "Find entities that connect multiple communities", "cypher": "MATCH (e:Entity)-[:RELATES_TO]-(other:Entity) WHERE e.group_id = $group_id WITH e, collect(DISTINCT other.community_id) as connected_communities WHERE size(connected_communities) > 1 MATCH (e)-[r:RELATES_TO]-(connected:Entity) RETURN e, r, connected", "parameters": {"group_id": "chatgpt_conversations"}}], "clustering_configuration": {"enable_auto_clustering": true, "clustering_algorithm": "louvain", "cluster_property": "community_id", "min_cluster_size": 2, "max_clusters": 20, "resolution": 1.0}, "search_configuration": {"enable_fulltext": true, "search_properties": {"Community": ["name", "summary"], "Entity": ["name", "summary"]}, "community_search": true, "fuzzy_search": true}, "export_settings": {"include_community_data": true, "include_clustering_info": true, "format": "json"}}, "metadata": {"created_by": "Graphiti Bloom Integration", "created_date": "2024-01-01", "version": "1.0", "compatible_with": ["Neo4j Bloom 2.0+"], "description": "Community-focused perspective for analyzing entity clustering and community structures"}}