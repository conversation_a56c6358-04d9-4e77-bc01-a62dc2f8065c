# Neo4j Bloom Perspectives for Graphiti

This directory contains pre-configured Neo4j Bloom perspectives optimized for visualizing Graphiti knowledge graphs.

## Available Perspectives

### 1. Entity Relationships (`entity_relationships.json`)
**Purpose**: Visualize how entities relate to each other in the knowledge graph

**Features**:
- Entities displayed as blue circles, sized by relationship count
- Communities shown as green hexagons
- Relationship facts displayed as edge labels
- Search enabled for entity names and summaries

**Best for**: Understanding entity connections and relationship patterns

### 2. Episode Timeline (`episode_timeline.json`)
**Purpose**: Temporal visualization of episodes and entity mentions

**Features**:
- Episodes displayed as orange rectangles with timestamps
- Entities shown as smaller blue circles
- Timeline layout based on creation dates
- Temporal filtering and animation controls

**Best for**: Analyzing conversation flow and entity mention patterns over time

### 3. Community Analysis (`community_analysis.json`)
**Purpose**: Analyze entity communities and clustering patterns

**Features**:
- Communities sized by member count
- Entities colored by community membership
- Cross-community relationships highlighted in red
- Community detection and clustering algorithms

**Best for**: Understanding entity groupings and community structures

## Installation Instructions

### Option 1: Manual Import (Recommended)

1. **Open Neo4j Bloom**
   - Access through Neo4j Desktop or Browser
   - Connect to your Graphiti database

2. **Import Perspectives**
   - In Bloom, go to "Perspectives" → "Import"
   - Select the JSON files from this directory
   - Configure any custom parameters (like `group_id`)

3. **Customize as Needed**
   - Adjust colors, sizes, and layouts to your preference
   - Modify queries for your specific data patterns

### Option 2: Automated Setup

1. **Run the Bloom Setup Script**
   ```bash
   python bloom_setup.py
   ```

2. **Follow the Generated Configuration**
   - The script will analyze your graph and create optimized perspectives
   - Import the generated `bloom_config_*.json` files

### Option 3: Docker Integration

1. **Use the Enhanced Docker Compose**
   ```bash
   # For basic Bloom support
   docker-compose -f docker-compose.yml up
   
   # For full enterprise Bloom features
   docker-compose -f docker-compose.bloom.yml up
   ```

2. **Perspectives Auto-Mounted**
   - Perspectives are automatically available in `/var/lib/neo4j/bloom_perspectives`
   - Access through Bloom's import functionality

## Customization Guide

### Modifying Node Styling

```json
{
  "styling": {
    "color": "#1f77b4",           // Node color
    "size": {
      "type": "property",         // Size by property value
      "property": "degree",       // Property to use for sizing
      "min_size": 15,            // Minimum node size
      "max_size": 50             // Maximum node size
    },
    "icon": "person",            // Node icon
    "shape": "circle"            // Node shape
  }
}
```

### Adding Custom Queries

```json
{
  "name": "My Custom Query",
  "description": "Description of what this query does",
  "cypher": "MATCH (n:Entity) WHERE n.group_id = $group_id RETURN n",
  "parameters": {
    "group_id": "my_group"
  }
}
```

### Configuring Filters

```json
{
  "filters": [
    {
      "property": "created_at",
      "type": "datetime",
      "range": true
    },
    {
      "property": "group_id",
      "type": "string",
      "default": "chatgpt_conversations"
    }
  ]
}
```

## Common Use Cases

### 1. Data Quality Validation
- Use Entity Relationships perspective to identify orphaned entities
- Check for missing or incorrect relationships
- Validate entity naming consistency

### 2. Conversation Analysis
- Use Episode Timeline to track conversation flow
- Identify frequently mentioned entities
- Analyze temporal patterns in discussions

### 3. Knowledge Discovery
- Use Community Analysis to find entity clusters
- Discover unexpected relationships between entities
- Identify bridge entities connecting different topics

### 4. Graph Evolution Monitoring
- Track how the knowledge graph grows over time
- Monitor relationship formation patterns
- Identify emerging topics and entities

## Performance Tips

### For Large Graphs (>10k nodes)
1. **Use Filters**: Always apply `group_id` or time-based filters
2. **Limit Results**: Start with `LIMIT 50` and expand gradually
3. **Index Properties**: Ensure frequently queried properties are indexed
4. **Batch Loading**: Load data in smaller chunks for better performance

### Memory Optimization
1. **Increase Neo4j Memory**: Use the enhanced Docker Compose settings
2. **Close Unused Perspectives**: Only keep active perspectives open
3. **Clear Cache**: Regularly clear Bloom's visualization cache

## Troubleshooting

### Common Issues

**"No data found"**
- Check your `group_id` parameter matches your data
- Verify Neo4j connection is working
- Ensure data exists with correct node labels

**"Slow performance"**
- Add `LIMIT` clauses to queries
- Create indexes on frequently queried properties
- Use more specific `WHERE` clauses

**"Perspective won't import"**
- Verify JSON syntax is valid
- Check Neo4j Bloom version compatibility
- Ensure all required properties exist in your data

### Getting Help

1. **Check the main documentation**: `docs/neo4j-bloom-integration.md`
2. **Run the analysis script**: `python bloom_setup.py`
3. **Validate your data**: Use the sample queries in Neo4j Browser first
4. **Neo4j Community**: Visit the Neo4j Community forum for Bloom-specific questions

## Contributing

To contribute new perspectives or improvements:

1. **Test thoroughly** with different data sizes and patterns
2. **Document use cases** and configuration options
3. **Follow naming conventions** for consistency
4. **Include sample queries** that demonstrate the perspective's value

## Version Compatibility

- **Neo4j**: 5.26+ (5.26.2 recommended)
- **Neo4j Bloom**: 2.0+ (latest version recommended)
- **Graphiti**: Compatible with all versions using the standard node/relationship schema

For the latest updates and additional perspectives, check the Graphiti repository.
