{"perspective": {"name": "Episode Timeline", "description": "Temporal visualization of episodes and entity mentions in Graphiti", "version": "1.0", "created_for": "Graphiti Knowledge Graph", "node_categories": [{"label": "Episodic", "styling": {"color": "#ff7f0e", "border_color": "#cc5500", "border_width": 2, "size": {"type": "property", "property": "content_length", "min_size": 20, "max_size": 60, "default_size": 30}, "icon": "message", "shape": "rectangle"}, "captions": [{"property": "name", "position": "bottom", "font_size": 11, "font_weight": "normal"}, {"property": "created_at", "position": "top", "font_size": 9, "color": "#666666", "format": "datetime"}], "search_properties": ["name", "content"], "tooltip_properties": ["name", "content", "created_at", "group_id"], "filters": [{"property": "group_id", "type": "string", "default": "chatgpt_conversations"}, {"property": "created_at", "type": "datetime", "range": true}]}, {"label": "Entity", "styling": {"color": "#1f77b4", "border_color": "#0d5aa7", "border_width": 1, "size": {"type": "fixed", "value": 20}, "icon": "tag", "shape": "circle"}, "captions": [{"property": "name", "position": "bottom", "font_size": 10, "font_weight": "normal"}], "search_properties": ["name"], "tooltip_properties": ["name", "summary", "created_at"], "filters": [{"property": "group_id", "type": "string"}]}], "relationship_categories": [{"type": "MENTIONS", "styling": {"color": "#999999", "thickness": 2, "arrow_size": "small", "line_style": "solid"}, "captions": [{"property": "created_at", "position": "middle", "font_size": 8, "format": "time", "background": true}], "tooltip_properties": ["created_at", "uuid"], "filters": [{"property": "created_at", "type": "datetime", "range": true}]}], "layout": {"algorithm": "timeline", "settings": {"timeline_property": "created_at", "direction": "horizontal", "spacing": 100, "group_by": "group_id", "sort_order": "ascending", "enable_clustering": true}}, "default_queries": [{"name": "Recent Episodes", "description": "Show episodes from the last 7 days", "cypher": "MATCH (e:Episodic) WHERE e.group_id = $group_id AND e.created_at > datetime() - duration('P7D') RETURN e ORDER BY e.created_at DESC LIMIT 20", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Episode Timeline with Enti<PERSON>", "description": "Show episodes and the entities they mention", "cypher": "MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity) WHERE e.group_id = $group_id RETURN e, entity ORDER BY e.created_at DESC LIMIT 30", "parameters": {"group_id": "chatgpt_conversations"}}, {"name": "Entity Mention Timeline", "description": "Show when specific entities were mentioned", "cypher": "MATCH (e:Episodic)-[m:MENTIONS]->(entity:Entity) WHERE entity.name CONTAINS $entity_name AND e.group_id = $group_id RETURN e, m, entity ORDER BY e.created_at", "parameters": {"group_id": "chatgpt_conversations", "entity_name": ""}}, {"name": "Daily Episode Summary", "description": "Group episodes by day", "cypher": "MATCH (e:Episodic) WHERE e.group_id = $group_id AND e.created_at > datetime() - duration('P30D') WITH date(e.created_at) as day, collect(e) as episodes RETURN day, episodes ORDER BY day DESC", "parameters": {"group_id": "chatgpt_conversations"}}], "time_controls": {"enable_timeline_slider": true, "time_property": "created_at", "time_format": "datetime", "animation_speed": "medium", "step_size": "1 day", "default_range": "last_30_days"}, "search_configuration": {"enable_fulltext": true, "search_properties": {"Episodic": ["name", "content"], "Entity": ["name"]}, "temporal_search": true, "fuzzy_search": true}, "export_settings": {"include_timeline_data": true, "include_temporal_filters": true, "format": "json"}}, "metadata": {"created_by": "Graphiti Bloom Integration", "created_date": "2024-01-01", "version": "1.0", "compatible_with": ["Neo4j Bloom 2.0+"], "description": "Timeline-focused perspective for exploring episodic data and entity mentions over time"}}