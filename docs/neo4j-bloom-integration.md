# Neo4j Bloom Integration with Graphiti

Neo4j Bloom is a powerful graph visualization tool that provides an intuitive interface for exploring and analyzing graph data. This guide explains how to use Neo4j Bloom with your Graphiti knowledge graphs.

## Overview

Graphiti stores data in Neo4j with a well-defined schema that's perfectly suited for visualization in Neo4j Bloom:

### Node Types
- **`:Entity`** - Main entities extracted from conversations/episodes
  - Properties: `name`, `summary`, `uuid`, `group_id`, `created_at`
- **`:Episodic`** - Episodes/events/conversations 
  - Properties: `content`, `name`, `uuid`, `group_id`, `created_at`
- **`:Community`** - Community clusters for entity grouping
  - Properties: `name`, `summary`, `uuid`, `group_id`

### Relationship Types
- **`:RELATES_TO`** - Connections between entities
  - Properties: `fact`, `name`, `uuid`, `created_at`, `valid_at`
- **`:MENTIONS`** - References from episodes to entities
  - Properties: `uuid`, `created_at`

## Prerequisites

1. **Neo4j Database** - Running Neo4j 5.26+ with Graphiti data
2. **Neo4j Bloom** - Available through:
   - Neo4j Desktop (recommended for local development)
   - Neo4j Browser (limited visualization features)
   - Neo4j Aura (cloud-hosted with Bloom included)

## Setup Instructions

### Option 1: Using Neo4j Desktop (Recommended)

1. **Install Neo4j Desktop**
   - Download from [neo4j.com/download](https://neo4j.com/download/)
   - Create a new project or use existing

2. **Connect to Your Graphiti Database**
   - If using Docker Compose: Connect to `bolt://localhost:7687`
   - Username: `neo4j`
   - Password: `password` (or your configured password)

3. **Access Bloom**
   - Open your database in Neo4j Desktop
   - Click "Open with" → "Neo4j Bloom"

### Option 2: Using Neo4j Aura (Cloud)

1. **Create Neo4j Aura Instance**
   - Visit [neo4j.com/aura](https://neo4j.com/aura)
   - Create a new AuraDB instance
   - Note the connection URI and credentials

2. **Migrate Your Data**
   - Export data from local Neo4j: `CALL apoc.export.cypher.all("backup.cypher", {})`
   - Import to Aura using Neo4j Browser or Desktop

3. **Access Bloom**
   - Bloom is included with all Aura instances
   - Access through the Aura console

## Connection Configuration

### Default Graphiti Connection Settings

```bash
# Environment variables for Graphiti
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
```

### Bloom Connection Parameters

When connecting Bloom to your Graphiti database:

- **Connection URL**: `bolt://localhost:7687`
- **Username**: `neo4j`
- **Password**: `password`
- **Database**: `neo4j` (default)

## Creating Bloom Perspectives

Bloom uses "perspectives" to define how your graph data should be visualized. Here are recommended perspectives for Graphiti data:

### 1. Entity Relationship Perspective

**Purpose**: Visualize how entities relate to each other

**Configuration**:
- **Node Categories**: 
  - Entity (color: blue, size by degree)
  - Community (color: green, larger size)
- **Relationship Categories**:
  - RELATES_TO (show `fact` as label)
- **Search**: Enable search by `name` and `summary`

### 2. Episode Timeline Perspective

**Purpose**: Show temporal flow of episodes and entity mentions

**Configuration**:
- **Node Categories**:
  - Episodic (color: orange, size by content length)
  - Entity (color: blue, smaller size)
- **Relationship Categories**:
  - MENTIONS (show creation time)
- **Layout**: Timeline based on `created_at`

### 3. Community Analysis Perspective

**Purpose**: Analyze entity communities and clusters

**Configuration**:
- **Node Categories**:
  - Community (color: green, large size)
  - Entity (color by community membership)
- **Clustering**: Group by `group_id`

## Sample Queries for Bloom

### Basic Entity Exploration

```cypher
// View all entities in a specific group
MATCH (n:Entity) 
WHERE n.group_id = 'chatgpt_conversations' 
RETURN n LIMIT 50

// Find highly connected entities
MATCH (n:Entity)-[r:RELATES_TO]-(m:Entity)
WHERE n.group_id = 'chatgpt_conversations'
RETURN n, r, m
ORDER BY size((n)-[:RELATES_TO]-()) DESC
LIMIT 20
```

### Temporal Analysis

```cypher
// Recent episodes and their entities
MATCH (e:Episodic)-[:MENTIONS]->(entity:Entity)
WHERE e.group_id = 'chatgpt_conversations'
  AND e.created_at > datetime() - duration('P7D')
RETURN e, entity
ORDER BY e.created_at DESC

// Entity relationship timeline
MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity)
WHERE n.group_id = 'chatgpt_conversations'
  AND r.created_at IS NOT NULL
RETURN n, r, m
ORDER BY r.created_at DESC
LIMIT 30
```

### Community Discovery

```cypher
// Find entity communities
MATCH (c:Community)<-[:BELONGS_TO]-(e:Entity)
WHERE c.group_id = 'chatgpt_conversations'
RETURN c, e
LIMIT 100

// Cross-community relationships
MATCH (e1:Entity)-[r:RELATES_TO]->(e2:Entity)
WHERE e1.group_id = 'chatgpt_conversations'
  AND e1.community_id <> e2.community_id
RETURN e1, r, e2
```

## Visualization Best Practices

### 1. Start Small
- Begin with `LIMIT 20-50` nodes
- Gradually expand based on insights
- Use filters to focus on specific time periods or groups

### 2. Use Meaningful Colors
- **Entities**: Blue (primary data)
- **Episodes**: Orange (temporal events)  
- **Communities**: Green (groupings)
- **Relationships**: Gray with labels

### 3. Size by Importance
- **Entities**: Size by relationship count
- **Episodes**: Size by content length or mention count
- **Communities**: Size by member count

### 4. Leverage Time Filters
- Filter by `created_at` for temporal analysis
- Use date ranges for specific periods
- Animate timeline for dynamic exploration

## Troubleshooting

### Connection Issues

1. **Cannot connect to database**
   - Verify Neo4j is running: `docker-compose ps`
   - Check connection details match your setup
   - Ensure ports 7474 (HTTP) and 7687 (Bolt) are accessible

2. **Empty graph in Bloom**
   - Verify data exists: Run queries in Neo4j Browser first
   - Check `group_id` filters in your queries
   - Ensure proper node labels (`:Entity`, `:Episodic`)

### Performance Issues

1. **Slow loading**
   - Add `LIMIT` clauses to queries
   - Create indexes on frequently queried properties
   - Use more specific `WHERE` clauses

2. **Memory issues**
   - Reduce visualization scope
   - Increase Neo4j memory settings in Docker
   - Use pagination for large datasets

## Advanced Features

### Custom Styling
- Create custom node icons for different entity types
- Use conditional formatting based on properties
- Apply graph layouts (force-directed, hierarchical, circular)

### Interactive Exploration
- Use Bloom's search functionality for entity discovery
- Create saved searches for common patterns
- Share perspectives with team members

### Integration with Graphiti Workflows
- Use Bloom for data quality validation
- Identify relationship patterns for prompt engineering
- Monitor graph growth and evolution over time

## Next Steps

1. **Explore the sample perspectives** provided in this repository
2. **Create custom perspectives** for your specific use cases  
3. **Set up automated exports** for sharing visualizations
4. **Integrate with reporting workflows** using Bloom's export features

For more advanced Bloom features, consult the [official Neo4j Bloom documentation](https://neo4j.com/docs/bloom-user-guide/).
